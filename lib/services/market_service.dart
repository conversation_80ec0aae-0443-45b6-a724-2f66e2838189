import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:supabase_flutter/supabase_flutter.dart';

import '../brokers/ticks_model.dart';
import '../core/constants.dart';
import 'auth_client.dart';
import 'timer_manager.dart';

/// Enhanced market service with robust error handling, retry logic, and caching
/// Replaces the deprecated broker manager with server-side operations
class MarketService {
  static final MarketService _instance = MarketService._();
  factory MarketService() => _instance;
  MarketService._();

  final _httpClient = http.Client();
  final _authClient = AuthClient();

  // Configuration - all server-side, no environment dependencies
  static String get _baseUrl => MarketServiceConfig.baseUrl;
  static const Duration _defaultTimeout = MarketServiceConfig.requestTimeout;
  static const Duration _cacheTimeout = MarketServiceConfig.cacheTimeout;
  static const int _maxRetries = MarketServiceConfig.maxRetries;

  // Cache for symbols and market data with size limit
  final Map<String, CachedResponse> _cache = {};
  final int _maxCacheSize = MarketServiceConfig.maxCacheSize;

  // Real-time subscription management with better resource handling
  final Map<String, StreamController<SymbolPriceDto>> _priceStreams = {};
  final Set<String> _subscribedSymbols = {};
  final Map<String, StreamSubscription> _priceSubscriptions = {};

  // Centralized timer manager
  final TimerManager _timerManager = TimerManager();

  // Statistics tracking
  int _requestCount = 0;
  DateTime? _lastRequestTime;

  // Rate limiting
  final List<DateTime> _requestTimes = [];
  final Duration _rateLimitWindow = MarketServiceConfig.rateLimitWindow;
  final int _maxRequestsPerMinute = MarketServiceConfig.maxRequestsPerMinute;

  // Thread safety
  final Completer<void> _initCompleter = Completer<void>();
  bool _isInitialized = false;
  bool _isDisposed = false;
  final Set<Future> _pendingRequests = {};

  // Connection state
  bool _isConnected = true;

  /// Initialize the service
  Future<void> initialize() async {
    if (_isInitialized) return _initCompleter.future;
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    try {
      // Initialize AuthClient first
      await _authClient.initialize();

      // Initialize centralized timer manager
      _timerManager.initialize();

      await _setupCacheCleanup();
      await _startPriceUpdates();
      await _startConnectionMonitoring();

      _isInitialized = true;
      if (!_initCompleter.isCompleted) {
        _initCompleter.complete();
      }
    } catch (e) {
      if (!_initCompleter.isCompleted) {
        _initCompleter.completeError(e);
      }
      rethrow;
    }
  }

  /// Dispose resources
  Future<void> dispose() async {
    if (_isDisposed) return;
    _isDisposed = true;

    // Unregister from timer manager
    _timerManager.unregisterCallback('market_service_cache_cleanup');
    _timerManager.unregisterCallback('market_service_price_updates');
    _timerManager.unregisterCallback('market_service_connection_check');

    // Cancel all subscriptions
    for (final subscription in _priceSubscriptions.values) {
      await subscription.cancel();
    }
    _priceSubscriptions.clear();

    // Close all price streams
    for (final controller in _priceStreams.values) {
      await controller.close();
    }
    _priceStreams.clear();

    // Wait for pending requests to complete with timeout
    if (_pendingRequests.isNotEmpty) {
      try {
        await Future.wait(
          _pendingRequests.toList(),
        ).timeout(const Duration(seconds: 5));
      } catch (e) {
        debugPrint('Some requests didn\'t complete during dispose: $e');
      }
    }

    // Close HTTP client
    _httpClient.close();

    // Clear caches
    _cache.clear();
    _subscribedSymbols.clear();
    _requestTimes.clear();
  }

  /// Start connection monitoring
  Future<void> _startConnectionMonitoring() async {
    _timerManager.registerConnectionCheckCallback(
      _checkConnection,
      name: 'market_service_connection_check',
    );
  }

  /// Check connection status
  Future<void> _checkConnection() async {
    try {
      final response = await _httpClient
          .get(Uri.parse('$_baseUrl/health'))
          .timeout(const Duration(seconds: 5));

      _isConnected = response.statusCode == 200;
    } catch (e) {
      _isConnected = false;
      debugPrint('Connection check failed: $e');
    }
  }

  /// Get authentication headers for API calls
  Map<String, String> get _headers {
    final token = _authClient.accessToken;
    if (token != null) {
      debugPrint(
        '🔑 Using token (first 50 chars): ${token.substring(0, math.min(50, token.length))}...',
      );
      debugPrint('🔑 Token length: ${token.length}');

      // Try to decode JWT payload for debugging
      try {
        final parts = token.split('.');
        if (parts.length == 3) {
          // Decode the payload (second part)
          String payload = parts[1];
          // Add padding if needed
          while (payload.length % 4 != 0) {
            payload += '=';
          }
          final decoded = utf8.decode(base64Url.decode(payload));
          final claims = json.decode(decoded);
          debugPrint('🔍 JWT Claims: iss=${claims['iss']}, aud=${claims['aud']}, sub=${claims['sub']}, exp=${claims['exp']}');
        }
      } catch (e) {
        debugPrint('⚠️ Failed to decode JWT: $e');
      }
    } else {
      debugPrint('❌ No access token available');
    }
    return {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  /// Check if user is authenticated
  bool get isAuthenticated => _authClient.isAuthenticated;

  /// Get service status
  ServiceStatus get serviceStatus {
    return ServiceStatus(
      isInitialized: _isInitialized,
      isDisposed: _isDisposed,
      isConnected: _isConnected,
      isAuthenticated: isAuthenticated,
      cacheSize: _cache.length,
      subscribedSymbolsCount: _subscribedSymbols.length,
      requestCount: _requestCount,
      lastRequestTime: _lastRequestTime,
    );
  }

  // ============================================================================
  // STATISTICS AND MONITORING
  // ============================================================================

  /// Get current cache size
  int get cacheSize => _cache.length;

  /// Get total request count
  int get requestCount => _requestCount;

  /// Get last request time
  DateTime? get lastRequestTime => _lastRequestTime;

  /// Get connection status
  bool get isConnected => _isConnected;

  /// Get initialization status
  bool get isInitialized => _isInitialized;

  // ============================================================================
  // ERROR HANDLING AND RETRY LOGIC
  // ============================================================================

  /// Check rate limiting
  bool _isRateLimited() {
    final now = DateTime.now();

    // Remove old requests outside the window
    _requestTimes.removeWhere(
      (time) => now.difference(time) > _rateLimitWindow,
    );

    return _requestTimes.length >= _maxRequestsPerMinute;
  }

  /// Wait for rate limit to be available
  Future<void> _waitForRateLimit() async {
    if (!_isRateLimited()) return;

    final now = DateTime.now();
    if (_requestTimes.isNotEmpty) {
      final oldestRequest = _requestTimes.first;
      final waitTime = _rateLimitWindow - now.difference(oldestRequest);

      if (waitTime > Duration.zero) {
        debugPrint('Rate limited, waiting ${waitTime.inMilliseconds}ms');
        await Future.delayed(waitTime);
      }
    }
  }

  /// Execute HTTP request with retry logic and error handling
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    String? operationName,
  }) async {
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    await _waitForRateLimit();

    Exception? lastException;
    Duration currentDelay = MarketServiceConfig.initialRetryDelay;

    for (int attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        final future = operation();
        _pendingRequests.add(future);

        try {
          final result = await future;
          return result;
        } finally {
          _pendingRequests.remove(future);
        }
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());

        // Don't retry on authentication errors or client errors
        if (e is MarketServiceException) {
          if (e.message.contains('Authentication') ||
              e.message.contains('Access denied') ||
              e.message.contains('Invalid request')) {
            rethrow;
          }
        }

        if (attempt < maxRetries) {
          debugPrint(
            '${operationName ?? 'Operation'} failed (attempt ${attempt + 1}/${maxRetries + 1}): $e',
          );
          debugPrint('Retrying in ${currentDelay.inMilliseconds}ms...');

          await Future.delayed(currentDelay);

          // Exponential backoff with jitter
          currentDelay = Duration(
            milliseconds: math.min(
              currentDelay.inMilliseconds * 2,
              30000, // Max 30 seconds
            ),
          );

          // Add random jitter (up to 20% of delay)
          final jitter = Duration(
            milliseconds:
                (currentDelay.inMilliseconds * 0.2 * math.Random().nextDouble())
                    .round(),
          );
          currentDelay += jitter;
        }
      }
    }

    throw MarketServiceException(
      'Failed after ${maxRetries + 1} attempts: ${lastException?.toString()}',
      originalException: lastException,
    );
  }

  /// Make HTTP request with timeout and error handling
  Future<http.Response> _makeRequest(
    String method,
    String url, {
    Map<String, dynamic>? body,
    Duration? timeout,
  }) async {
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    // Update request statistics
    _requestCount++;
    _lastRequestTime = DateTime.now();
    _requestTimes.add(_lastRequestTime!);

    final uri = Uri.parse(url);
    final headers = _headers;
    final requestTimeout = timeout ?? _defaultTimeout;

    late http.Response response;

    try {
      switch (method.toUpperCase()) {
        case 'GET':
          response = await _httpClient
              .get(uri, headers: headers)
              .timeout(requestTimeout);
          break;
        case 'POST':
          response = await _httpClient
              .post(
                uri,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(requestTimeout);
          break;
        case 'PUT':
          response = await _httpClient
              .put(
                uri,
                headers: headers,
                body: body != null ? jsonEncode(body) : null,
              )
              .timeout(requestTimeout);
          break;
        case 'DELETE':
          response = await _httpClient
              .delete(uri, headers: headers)
              .timeout(requestTimeout);
          break;
        default:
          throw ArgumentError('Unsupported HTTP method: $method');
      }

      await _handleHttpResponse(response);
      return response;
    } on SocketException catch (e) {
      _isConnected = false;
      throw MarketServiceException('Network error: ${e.message}');
    } on TimeoutException catch (e) {
      throw MarketServiceException('Request timeout: ${e.message}');
    } on FormatException catch (e) {
      throw MarketServiceException('Invalid response format: ${e.message}');
    } on HandshakeException catch (e) {
      throw MarketServiceException('SSL handshake failed: ${e.message}');
    } on HttpException catch (e) {
      throw MarketServiceException('HTTP error: ${e.message}');
    } catch (e) {
      throw MarketServiceException('Unexpected error: ${e.toString()}');
    }
  }

  /// Handle HTTP response and throw appropriate exceptions
  Future<void> _handleHttpResponse(http.Response response) async {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return; // Success
    }

    String errorMessage = 'HTTP ${response.statusCode}';
    String? serverError;
    bool isDatabaseError = false;

    try {
      final errorBody = jsonDecode(response.body);
      if (errorBody is Map<String, dynamic>) {
        errorMessage =
            errorBody['message'] ?? errorBody['error'] ?? errorMessage;
        serverError = errorBody['error']?.toString();

        // Check for database connection issues
        if (serverError != null &&
            (serverError.contains('database') ||
                serverError.contains('connection') ||
                serverError.contains('postgresql') ||
                serverError.contains('Npgsql'))) {
          isDatabaseError = true;
        }
      }
    } catch (e) {
      // Use status code if can't parse error body
      errorMessage = 'HTTP ${response.statusCode}: ${response.reasonPhrase}';
    }

    switch (response.statusCode) {
      case 401:
        // Check for token expiration in headers
        final authHeader =
            response.headers['www-authenticate']?.toLowerCase() ?? '';
        if (authHeader.contains('invalid_token') ||
            authHeader.contains('expired')) {
          // Check if we have a recent Supabase session to avoid clearing fresh sessions
          final session = Supabase.instance.client.auth.currentSession;
          final sessionAge =
              session != null
                  ? DateTime.now().millisecondsSinceEpoch -
                      (session.expiresAt! * 1000 - 3600000)
                  : 999999;

          if (sessionAge >= 30000) {
            // 30 seconds in milliseconds
            debugPrint(
              'Token expired detected in MarketService, clearing auth state (age: ${sessionAge}ms)',
            );
            // Clear auth state to force re-authentication
            await _authClient.signOut();
          } else {
            debugPrint(
              'Token appears expired but session is recent (${sessionAge}ms ago), not clearing',
            );
          }
        }
        throw MarketServiceException('Authentication failed: $errorMessage');
      case 403:
        throw MarketServiceException('Access denied: $errorMessage');
      case 404:
        throw MarketServiceException('Resource not found: $errorMessage');
      case 429:
        throw MarketServiceException('Rate limit exceeded: $errorMessage');
      case 500:
        if (isDatabaseError) {
          throw MarketServiceException(
            'Server database connection error. Please try again later or contact support.',
            originalException: Exception(serverError),
          );
        }
        throw MarketServiceException('Server error: $errorMessage');
      default:
        throw MarketServiceException('Request failed: $errorMessage');
    }
  }

  // ============================================================================
  // CACHE MANAGEMENT
  // ============================================================================

  /// Setup cache cleanup timer
  Future<void> _setupCacheCleanup() async {
    _timerManager.registerCacheCleanupCallback(
      _cleanupExpiredCache,
      name: 'market_service_cache_cleanup',
    );
  }

  /// Clean up expired cache entries
  void _cleanupExpiredCache() {
    if (_isDisposed) return;

    final now = DateTime.now();
    final keysToRemove = <String>[];

    // Find expired entries
    for (final entry in _cache.entries) {
      if (now.difference(entry.value.timestamp) > _cacheTimeout) {
        keysToRemove.add(entry.key);
      }
    }

    // Remove expired entries
    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    // If cache is still too large, remove oldest entries
    if (_cache.length > _maxCacheSize) {
      final entries = _cache.entries.toList();
      entries.sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));

      final entriesToRemove = entries.take(_cache.length - _maxCacheSize);
      for (final entry in entriesToRemove) {
        _cache.remove(entry.key);
      }
    }

    debugPrint('Cache cleanup completed. Cache size: ${_cache.length}');
  }

  /// Get cached response if valid
  T? _getCachedResponse<T>(String key, {Duration? customTimeout}) {
    final cached = _cache[key];
    if (cached != null) {
      final timeout = customTimeout ?? _cacheTimeout;
      if (DateTime.now().difference(cached.timestamp) < timeout) {
        return cached.data as T?;
      } else {
        // Remove expired entry
        _cache.remove(key);
      }
    }
    return null;
  }

  /// Cache response with size management
  void _cacheResponse<T>(String key, T data, {Duration? customTimeout}) {
    // Remove old entry if exists
    _cache.remove(key);

    // Add new entry
    _cache[key] = CachedResponse(data, DateTime.now());

    // Check cache size and remove oldest if needed
    if (_cache.length > _maxCacheSize) {
      final entries = _cache.entries.toList();
      entries.sort((a, b) => a.value.timestamp.compareTo(b.value.timestamp));

      final oldestEntry = entries.first;
      _cache.remove(oldestEntry.key);
    }
  }

  /// Clear cache
  void clearCache() {
    _cache.clear();
    debugPrint('Cache cleared');
  }

  /// Clear cache for specific pattern
  void clearCachePattern(String pattern) {
    final keysToRemove =
        _cache.keys.where((key) => key.contains(pattern)).toList();

    for (final key in keysToRemove) {
      _cache.remove(key);
    }

    debugPrint(
      'Cleared ${keysToRemove.length} cache entries matching pattern: $pattern',
    );
  }

  // ============================================================================
  // SYMBOL DISCOVERY AND MANAGEMENT
  // ============================================================================

  /// Get available symbols from server
  Future<List<String>> getAvailableSymbols({bool forceRefresh = false}) async {
    final cacheKey = 'available_symbols';

    if (!forceRefresh) {
      final cached = _getCachedResponse<List<String>>(cacheKey);
      if (cached != null) return cached;
    }

    return await _executeWithRetry<List<String>>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/symbols',
      );
      final List<dynamic> data = jsonDecode(response.body);
      final symbols = data.map((symbol) => symbol.toString()).toList();

      _cacheResponse(cacheKey, symbols);
      return symbols;
    }, operationName: 'Get available symbols');
  }

  /// Search symbols by query
  Future<List<SymbolSearchResult>> searchSymbols(String query) async {
    if (query.isEmpty) return [];

    return await _executeWithRetry<List<SymbolSearchResult>>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/symbols/search?q=${Uri.encodeComponent(query)}',
      );
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => SymbolSearchResult.fromJson(json)).toList();
    }, operationName: 'Search symbols');
  }

  /// Get symbol metadata
  Future<SymbolMetadata?> getSymbolMetadata(String symbol) async {
    final cacheKey = 'symbol_metadata_$symbol';

    final cached = _getCachedResponse<SymbolMetadata>(cacheKey);
    if (cached != null) return cached;

    return await _executeWithRetry<SymbolMetadata?>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/symbols/$symbol/metadata',
      );
      final data = jsonDecode(response.body);
      final metadata = SymbolMetadata.fromJson(data);

      _cacheResponse(cacheKey, metadata);
      return metadata;
    }, operationName: 'Get symbol metadata');
  }

  // ============================================================================
  // LIVE PRICE OPERATIONS
  // ============================================================================

  /// Get live price for a single symbol
  Future<SymbolPriceDto?> fetchLivePrice(
    String symbol, {
    bool noCache = false,
  }) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    final cacheKey = 'live_price_$symbol';

    if (!noCache) {
      final cached = _getCachedResponse<SymbolPriceDto>(cacheKey);
      if (cached != null) return cached;
    }

    return await _executeWithRetry<SymbolPriceDto?>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/marketdata/$symbol/fetch-live?nocache=$noCache',
      );
      final data = jsonDecode(response.body);
      final price = SymbolPriceDto.fromJson(data);

      if (!noCache) {
        _cacheResponse(cacheKey, price);
      }

      return price;
    }, operationName: 'Fetch live price for $symbol');
  }

  /// Get bulk prices for multiple symbols using server's bulk endpoint
  Future<BulkPriceResponse> getBulkPrices(
    List<String> symbols, {
    bool bypassCache = false,
  }) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    if (symbols.isEmpty) {
      return BulkPriceResponse(
        prices: [],
        successCount: 0,
        failureCount: 0,
        failedSymbols: [],
        timestamp: DateTime.now(),
        processingTime: Duration.zero,
      );
    }

    if (symbols.length > 100) {
      throw MarketServiceException(
        'Maximum 100 symbols allowed for bulk prices',
      );
    }

    return await _executeWithRetry<BulkPriceResponse>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist/bulk-prices',
        body: {'symbols': symbols, 'bypassCache': bypassCache},
      );

      final data = jsonDecode(response.body);
      return BulkPriceResponse.fromJson(data);
    }, operationName: 'Get bulk prices');
  }

  // ============================================================================
  // REAL-TIME PRICE STREAMING
  // ============================================================================

  /// Subscribe to real-time price updates for a symbol
  Stream<SymbolPriceDto> subscribeToSymbolPrice(String symbol) {
    if (_isDisposed) {
      throw MarketServiceException('Service has been disposed');
    }

    if (!_priceStreams.containsKey(symbol)) {
      _priceStreams[symbol] = StreamController<SymbolPriceDto>.broadcast(
        onCancel: () => _cleanupSymbolStream(symbol),
      );
    }

    _subscribedSymbols.add(symbol);
    debugPrint(
      'Subscribed to price updates for $symbol. Total subscriptions: ${_subscribedSymbols.length}',
    );

    return _priceStreams[symbol]!.stream;
  }

  /// Unsubscribe from real-time price updates
  void unsubscribeFromSymbolPrice(String symbol) {
    _subscribedSymbols.remove(symbol);
    debugPrint(
      'Unsubscribed from price updates for $symbol. Remaining subscriptions: ${_subscribedSymbols.length}',
    );

    // Don't immediately close the stream, let it clean up naturally
    // This prevents issues with multiple listeners
  }

  /// Clean up symbol stream when no longer needed
  void _cleanupSymbolStream(String symbol) {
    if (_priceStreams.containsKey(symbol)) {
      final controller = _priceStreams[symbol]!;
      if (!controller.isClosed) {
        controller.close();
      }
      _priceStreams.remove(symbol);
    }

    // Remove from subscriptions if still there
    _subscribedSymbols.remove(symbol);

    // Cancel any related subscriptions
    if (_priceSubscriptions.containsKey(symbol)) {
      _priceSubscriptions[symbol]?.cancel();
      _priceSubscriptions.remove(symbol);
    }
  }

  /// Start periodic price updates for subscribed symbols
  Future<void> _startPriceUpdates() async {
    _timerManager.registerPriceUpdateCallback(() async {
      if (_isDisposed) return;

      if (_subscribedSymbols.isNotEmpty && _isConnected) {
        await _updateSubscribedPrices();
      }
    }, name: 'market_service_price_updates');
  }

  /// Update prices for all subscribed symbols
  Future<void> _updateSubscribedPrices() async {
    if (_subscribedSymbols.isEmpty || _isDisposed) return;

    try {
      final symbolsList = _subscribedSymbols.toList();
      final response = await getBulkPrices(symbolsList, bypassCache: true);

      // Update streams and cache
      for (final price in response.prices) {
        final controller = _priceStreams[price.symbol];
        if (controller != null && !controller.isClosed) {
          controller.add(price);
        }

        // Update cache with shorter timeout for real-time data
        _cacheResponse(
          'live_price_${price.symbol}',
          price,
          customTimeout: MarketServiceConfig.priceCacheTimeout,
        );
      }

      // Handle failed symbols
      for (final failedSymbol in response.failedSymbols) {
        final controller = _priceStreams[failedSymbol];
        if (controller != null && !controller.isClosed) {
          // Create an error price object
          final errorPrice = SymbolPriceDto(
            symbol: failedSymbol,
            status: 'error',
            currentPrice: null,
            priceChange: null,
            priceChangePercent: null,
            lastUpdated: DateTime.now(),
          );
          controller.add(errorPrice);
        }
      }

      debugPrint(
        'Updated prices for ${response.successCount}/${symbolsList.length} symbols',
      );
    } catch (e) {
      debugPrint('Error updating subscribed prices: $e');

      // Send error status to all subscribed streams
      for (final symbol in _subscribedSymbols) {
        final controller = _priceStreams[symbol];
        if (controller != null && !controller.isClosed) {
          final errorPrice = SymbolPriceDto(
            symbol: symbol,
            status: 'error',
            currentPrice: null,
            priceChange: null,
            priceChangePercent: null,
            lastUpdated: DateTime.now(),
          );
          controller.add(errorPrice);
        }
      }
    }
  }

  // ============================================================================
  // HISTORICAL DATA OPERATIONS
  // ============================================================================

  /// Get historical price data for a symbol
  Future<List<HistoricalPriceDto>> getHistoricalPrices(
    String symbol, {
    required DateTime from,
    required DateTime to,
    String interval = '1d',
    bool useCache = true,
  }) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    final cacheKey =
        'historical_${symbol}_${from.toIso8601String()}_${to.toIso8601String()}_$interval';

    if (useCache) {
      final cached = _getCachedResponse<List<HistoricalPriceDto>>(cacheKey);
      if (cached != null) return cached;
    }

    return await _executeWithRetry<List<HistoricalPriceDto>>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/marketdata/$symbol/history'
            '?from=${from.toIso8601String()}'
            '&to=${to.toIso8601String()}'
            '&interval=$interval',
      );

      final List<dynamic> data = jsonDecode(response.body);
      final prices =
          data.map((json) => HistoricalPriceDto.fromJson(json)).toList();

      if (useCache) {
        _cacheResponse(cacheKey, prices);
      }

      return prices;
    }, operationName: 'Get historical prices for $symbol');
  }

  /// Get price analytics for symbols
  Future<List<PriceAnalyticsDto>> getPriceAnalytics(
    List<String> symbols,
  ) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    if (symbols.isEmpty) return [];

    return await _executeWithRetry<List<PriceAnalyticsDto>>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/marketdata/analytics',
        body: {'symbols': symbols},
      );

      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => PriceAnalyticsDto.fromJson(json)).toList();
    }, operationName: 'Get price analytics');
  }

  // ============================================================================
  // WATCHLIST OPERATIONS
  // ============================================================================

  /// Get all watchlists for the current user
  Future<List<WatchlistDto>> getWatchlists() async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<List<WatchlistDto>>(() async {
      final response = await _makeRequest('GET', '$_baseUrl/api/watchlist');
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => WatchlistDto.fromJson(json)).toList();
    }, operationName: 'Get watchlists');
  }

  /// Get watchlist with live prices
  Future<WatchlistWithPricesDto> getWatchlistWithPrices(int watchlistId) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<WatchlistWithPricesDto>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/watchlist/$watchlistId/with-prices',
      );
      final data = jsonDecode(response.body);
      return WatchlistWithPricesDto.fromJson(data);
    }, operationName: 'Get watchlist with prices');
  }

  /// Create a new watchlist
  Future<WatchlistDto> createWatchlist(String name) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<WatchlistDto>(() async {
      final response = await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist',
        body: {'name': name},
      );
      final data = jsonDecode(response.body);
      return WatchlistDto.fromJson(data);
    }, operationName: 'Create watchlist');
  }

  /// Add symbol to watchlist
  Future<void> addSymbolToWatchlist(int watchlistId, String symbol) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    await _executeWithRetry<void>(() async {
      await _makeRequest(
        'POST',
        '$_baseUrl/api/watchlist/$watchlistId/symbols',
        body: {
          'symbols': [symbol],
        },
      );
    }, operationName: 'Add symbol to watchlist');
  }

  /// Remove symbol from watchlist
  Future<void> removeSymbolFromWatchlist(int watchlistId, String symbol) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    await _executeWithRetry<void>(() async {
      await _makeRequest(
        'DELETE',
        '$_baseUrl/api/watchlist/$watchlistId/symbols/$symbol',
      );
    }, operationName: 'Remove symbol from watchlist');
  }

  /// Delete watchlist
  Future<void> deleteWatchlist(int watchlistId) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    await _executeWithRetry<void>(() async {
      await _makeRequest('DELETE', '$_baseUrl/api/watchlist/$watchlistId');
    }, operationName: 'Delete watchlist');
  }

  /// Rename watchlist
  Future<WatchlistDto> renameWatchlist(int watchlistId, String newName) async {
    if (!isAuthenticated) {
      throw MarketServiceException('User not authenticated');
    }

    return await _executeWithRetry<WatchlistDto>(() async {
      final response = await _makeRequest(
        'PUT',
        '$_baseUrl/api/watchlist/$watchlistId',
        body: {'name': newName},
      );
      final data = jsonDecode(response.body);
      return WatchlistDto.fromJson(data);
    }, operationName: 'Rename watchlist');
  }

  // ============================================================================
  // BROKER OPERATIONS
  // ============================================================================

  /// Get available brokers
  Future<List<BrokerMetadata>> getAvailableBrokers() async {
    return await _executeWithRetry<List<BrokerMetadata>>(() async {
      final response = await _makeRequest('GET', '$_baseUrl/api/brokers');
      final List<dynamic> data = jsonDecode(response.body);
      return data.map((json) => BrokerMetadata.fromJson(json)).toList();
    }, operationName: 'Get available brokers');
  }

  /// Get broker metadata
  Future<BrokerMetadata?> getBrokerMetadata(String brokerId) async {
    return await _executeWithRetry<BrokerMetadata?>(() async {
      final response = await _makeRequest(
        'GET',
        '$_baseUrl/api/brokers/$brokerId',
      );
      final data = jsonDecode(response.body);
      return BrokerMetadata.fromJson(data);
    }, operationName: 'Get broker metadata');
  }
}

// ============================================================================
// EXCEPTION CLASSES
// ============================================================================

class MarketServiceException implements Exception {
  final String message;
  final Exception? originalException;

  MarketServiceException(this.message, {this.originalException});

  @override
  String toString() => 'MarketServiceException: $message';
}

// ============================================================================
// CACHE HELPER CLASSES
// ============================================================================

class CachedResponse {
  final dynamic data;
  final DateTime timestamp;

  CachedResponse(this.data, this.timestamp);
}

// ============================================================================
// ENHANCED DATA MODELS
// ============================================================================

class SymbolSearchResult {
  final String symbol;
  final String name;
  final String? exchange;
  final String? type;
  final String? currency;

  SymbolSearchResult({
    required this.symbol,
    required this.name,
    this.exchange,
    this.type,
    this.currency,
  });

  factory SymbolSearchResult.fromJson(Map<String, dynamic> json) {
    return SymbolSearchResult(
      symbol: json['symbol'] as String,
      name: json['name'] as String,
      exchange: json['exchange'] as String?,
      type: json['type'] as String?,
      currency: json['currency'] as String?,
    );
  }
}

class SymbolMetadata {
  final String symbol;
  final String name;
  final String? description;
  final String? exchange;
  final String? type;
  final String? currency;
  final String? sector;
  final String? industry;
  final double? marketCap;
  final int? sharesOutstanding;

  SymbolMetadata({
    required this.symbol,
    required this.name,
    this.description,
    this.exchange,
    this.type,
    this.currency,
    this.sector,
    this.industry,
    this.marketCap,
    this.sharesOutstanding,
  });

  factory SymbolMetadata.fromJson(Map<String, dynamic> json) {
    return SymbolMetadata(
      symbol: json['symbol'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      exchange: json['exchange'] as String?,
      type: json['type'] as String?,
      currency: json['currency'] as String?,
      sector: json['sector'] as String?,
      industry: json['industry'] as String?,
      marketCap: (json['marketCap'] as num?)?.toDouble(),
      sharesOutstanding: json['sharesOutstanding'] as int?,
    );
  }
}

class BrokerMetadata {
  final String id;
  final String name;
  final String? description;
  final bool isActive;
  final List<String> supportedFeatures;

  BrokerMetadata({
    required this.id,
    required this.name,
    this.description,
    required this.isActive,
    required this.supportedFeatures,
  });

  factory BrokerMetadata.fromJson(Map<String, dynamic> json) {
    return BrokerMetadata(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String?,
      isActive: json['isActive'] as bool,
      supportedFeatures:
          (json['supportedFeatures'] as List<dynamic>)
              .map((feature) => feature as String)
              .toList(),
    );
  }
}

// Import DTOs from the existing market service
class SymbolPriceDto {
  final String symbol;
  final double? currentPrice;
  final double? previousPrice;
  final double? priceChange;
  final double? priceChangePercent;
  final double? volume;
  final DateTime? lastUpdated;
  final String status;
  final String? brokerId;

  const SymbolPriceDto({
    required this.symbol,
    this.currentPrice,
    this.previousPrice,
    this.priceChange,
    this.priceChangePercent,
    this.volume,
    this.lastUpdated,
    required this.status,
    this.brokerId,
  });

  factory SymbolPriceDto.fromJson(Map<String, dynamic> json) {
    return SymbolPriceDto(
      symbol: json['symbol'] as String,
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      previousPrice: (json['previousPrice'] as num?)?.toDouble(),
      priceChange: (json['priceChange'] as num?)?.toDouble(),
      priceChangePercent: (json['priceChangePercent'] as num?)?.toDouble(),
      volume: (json['volume'] as num?)?.toDouble(),
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.parse(json['lastUpdated'] as String)
              : null,
      status: json['status'] as String,
      brokerId: json['brokerId'] as String?,
    );
  }
}

class BulkPriceResponse {
  final List<SymbolPriceDto> prices;
  final int successCount;
  final int failureCount;
  final List<String> failedSymbols;
  final DateTime timestamp;
  final String? cacheStatus;
  final Duration processingTime;

  const BulkPriceResponse({
    required this.prices,
    required this.successCount,
    required this.failureCount,
    required this.failedSymbols,
    required this.timestamp,
    this.cacheStatus,
    required this.processingTime,
  });

  factory BulkPriceResponse.fromJson(Map<String, dynamic> json) {
    return BulkPriceResponse(
      prices:
          (json['prices'] as List<dynamic>)
              .map((price) => SymbolPriceDto.fromJson(price))
              .toList(),
      successCount: json['successCount'] as int,
      failureCount: json['failureCount'] as int,
      failedSymbols:
          (json['failedSymbols'] as List<dynamic>)
              .map((symbol) => symbol as String)
              .toList(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      cacheStatus: json['cacheStatus'] as String?,
      processingTime: _parseTimeSpan(json['processingTime'] as String),
    );
  }

  static Duration _parseTimeSpan(String timeSpan) {
    final parts = timeSpan.split(':');
    if (parts.length < 3) return Duration.zero;

    final hours = int.parse(parts[0]);
    final minutes = int.parse(parts[1]);
    final secondsParts = parts[2].split('.');
    final seconds = int.parse(secondsParts[0]);
    final milliseconds =
        secondsParts.length > 1
            ? int.parse(secondsParts[1].padRight(3, '0').substring(0, 3))
            : 0;

    return Duration(
      hours: hours,
      minutes: minutes,
      seconds: seconds,
      milliseconds: milliseconds,
    );
  }
}

class WatchlistDto {
  final int id;
  final String name;
  final String userId;
  final DateTime createdAt;
  final bool isDefault;

  const WatchlistDto({
    required this.id,
    required this.name,
    required this.userId,
    required this.createdAt,
    required this.isDefault,
  });

  factory WatchlistDto.fromJson(Map<String, dynamic> json) {
    return WatchlistDto(
      id: json['id'] as int,
      name: json['name'] as String,
      userId: json['userId'] as String? ?? json['user_id'] as String? ?? '',
      createdAt: DateTime.parse(
        json['createdAt'] as String? ??
            json['created_at'] as String? ??
            DateTime.now().toIso8601String(),
      ),
      isDefault:
          json['isDefault'] as bool? ?? json['is_default'] as bool? ?? false,
    );
  }
}

class WatchlistWithPricesDto {
  final List<WatchlistItemDto> items;
  final DateTime pricesLastUpdated;

  const WatchlistWithPricesDto({
    required this.items,
    required this.pricesLastUpdated,
  });

  factory WatchlistWithPricesDto.fromJson(Map<String, dynamic> json) {
    return WatchlistWithPricesDto(
      items:
          (json['items'] as List<dynamic>)
              .map((item) => WatchlistItemDto.fromJson(item))
              .toList(),
      pricesLastUpdated: DateTime.parse(json['pricesLastUpdated'] as String),
    );
  }
}

class WatchlistItemDto {
  final String symbol;
  final int sortOrder;
  final DateTime addedAt;
  final double? currentPrice;
  final double? previousPrice;
  final double? priceChange;
  final double? priceChangePercent;
  final double? volume;
  final DateTime? lastUpdated;
  final String status;

  const WatchlistItemDto({
    required this.symbol,
    required this.sortOrder,
    required this.addedAt,
    this.currentPrice,
    this.previousPrice,
    this.priceChange,
    this.priceChangePercent,
    this.volume,
    this.lastUpdated,
    required this.status,
  });

  factory WatchlistItemDto.fromJson(Map<String, dynamic> json) {
    return WatchlistItemDto(
      symbol: json['symbol'] as String,
      sortOrder: json['sortOrder'] as int,
      addedAt: DateTime.parse(json['addedAt'] as String),
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      previousPrice: (json['previousPrice'] as num?)?.toDouble(),
      priceChange: (json['priceChange'] as num?)?.toDouble(),
      priceChangePercent: (json['priceChangePercent'] as num?)?.toDouble(),
      volume: (json['volume'] as num?)?.toDouble(),
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.parse(json['lastUpdated'] as String)
              : null,
      status: json['status'] as String,
    );
  }
}

class HistoricalPriceDto {
  final DateTime date;
  final double open;
  final double high;
  final double low;
  final double close;
  final int volume;

  const HistoricalPriceDto({
    required this.date,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  factory HistoricalPriceDto.fromJson(Map<String, dynamic> json) {
    return HistoricalPriceDto(
      date: DateTime.parse(json['date'] as String),
      open: (json['open'] as num).toDouble(),
      high: (json['high'] as num).toDouble(),
      low: (json['low'] as num).toDouble(),
      close: (json['close'] as num).toDouble(),
      volume: json['volume'] as int,
    );
  }

  /// Convert to client-side Candle model
  Candle toCandle() {
    return Candle(
      open: open,
      high: high,
      low: low,
      close: close,
      volume: volume.toDouble(),
      timestamp: date,
    );
  }
}

class PriceAnalyticsDto {
  final String symbol;
  final double? currentPrice;
  final double? dayChange;
  final double? dayChangePercent;
  final double? weekChange;
  final double? weekChangePercent;
  final double? monthChange;
  final double? monthChangePercent;
  final double? yearChange;
  final double? yearChangePercent;
  final double? fiftyTwoWeekHigh;
  final double? fiftyTwoWeekLow;
  final double? averageVolume;
  final double? marketCap;
  final DateTime? lastUpdated;

  const PriceAnalyticsDto({
    required this.symbol,
    this.currentPrice,
    this.dayChange,
    this.dayChangePercent,
    this.weekChange,
    this.weekChangePercent,
    this.monthChange,
    this.monthChangePercent,
    this.yearChange,
    this.yearChangePercent,
    this.fiftyTwoWeekHigh,
    this.fiftyTwoWeekLow,
    this.averageVolume,
    this.marketCap,
    this.lastUpdated,
  });

  factory PriceAnalyticsDto.fromJson(Map<String, dynamic> json) {
    return PriceAnalyticsDto(
      symbol: json['symbol'] as String,
      currentPrice: (json['currentPrice'] as num?)?.toDouble(),
      dayChange: (json['dayChange'] as num?)?.toDouble(),
      dayChangePercent: (json['dayChangePercent'] as num?)?.toDouble(),
      weekChange: (json['weekChange'] as num?)?.toDouble(),
      weekChangePercent: (json['weekChangePercent'] as num?)?.toDouble(),
      monthChange: (json['monthChange'] as num?)?.toDouble(),
      monthChangePercent: (json['monthChangePercent'] as num?)?.toDouble(),
      yearChange: (json['yearChange'] as num?)?.toDouble(),
      yearChangePercent: (json['yearChangePercent'] as num?)?.toDouble(),
      fiftyTwoWeekHigh: (json['fiftyTwoWeekHigh'] as num?)?.toDouble(),
      fiftyTwoWeekLow: (json['fiftyTwoWeekLow'] as num?)?.toDouble(),
      averageVolume: (json['averageVolume'] as num?)?.toDouble(),
      marketCap: (json['marketCap'] as num?)?.toDouble(),
      lastUpdated:
          json['lastUpdated'] != null
              ? DateTime.parse(json['lastUpdated'] as String)
              : null,
    );
  }
}

// ============================================================================
// SERVICE STATUS CLASS
// ============================================================================

class ServiceStatus {
  final bool isInitialized;
  final bool isDisposed;
  final bool isConnected;
  final bool isAuthenticated;
  final int cacheSize;
  final int subscribedSymbolsCount;
  final int requestCount;
  final DateTime? lastRequestTime;

  const ServiceStatus({
    required this.isInitialized,
    required this.isDisposed,
    required this.isConnected,
    required this.isAuthenticated,
    required this.cacheSize,
    required this.subscribedSymbolsCount,
    required this.requestCount,
    this.lastRequestTime,
  });

  /// Get overall service health status
  ServiceHealthStatus get healthStatus {
    if (isDisposed) return ServiceHealthStatus.disposed;
    if (!isInitialized) return ServiceHealthStatus.initializing;
    if (!isConnected) return ServiceHealthStatus.offline;
    if (!isAuthenticated) return ServiceHealthStatus.unauthenticated;
    return ServiceHealthStatus.healthy;
  }

  Map<String, dynamic> toJson() {
    return {
      'isInitialized': isInitialized,
      'isDisposed': isDisposed,
      'isConnected': isConnected,
      'isAuthenticated': isAuthenticated,
      'cacheSize': cacheSize,
      'subscribedSymbolsCount': subscribedSymbolsCount,
      'requestCount': requestCount,
      'lastRequestTime': lastRequestTime?.toIso8601String(),
      'healthStatus': healthStatus.name,
    };
  }

  @override
  String toString() {
    return 'ServiceStatus(${toJson()})';
  }
}

enum ServiceHealthStatus {
  healthy,
  initializing,
  offline,
  unauthenticated,
  disposed,
}
