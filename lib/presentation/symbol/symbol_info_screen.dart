import 'package:abra/presentation/symbol/widgets/orderbook_tab.dart';
import 'package:abra/presentation/symbol/widgets/symbol_history_tab.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../services/market_service.dart';
import 'providers/symbol_provider.dart';
import 'widgets/symbol_chart_section.dart';
import 'widgets/symbol_info_tab.dart';
import 'widgets/symbol_news_tab.dart';

class SymbolInfoScreen extends ConsumerStatefulWidget {
  final String symbol;
  final WatchlistItemDto? initialData;

  const SymbolInfoScreen({super.key, required this.symbol, this.initialData});

  @override
  ConsumerState<SymbolInfoScreen> createState() => _SymbolInfoScreenState();
}

class _SymbolInfoScreenState extends ConsumerState<SymbolInfoScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    // Set the selected symbol for chart integration
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(selectedSymbolProvider.notifier).updateSymbol(widget.symbol);
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context).colorScheme;

    return Scaffold(
      backgroundColor: theme.primary,
      appBar: _buildAppBar(theme),
      body: Column(
        children: [
          // Chart section
          Expanded(flex: 2, child: ChartSection()),
          // Tab section
          Expanded(flex: 3, child: _buildTabSection(theme)),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(ColorScheme theme) {
    return AppBar(
      backgroundColor: theme.primary,
      elevation: 0,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.pop(context),
      ),
      title: Row(
        children: [
          Text(
            widget.symbol,
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          const SizedBox(width: 8),
          const Icon(Icons.keyboard_arrow_down),
        ],
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.star_border),
          onPressed: () {
            //Add to watchlist functionality
          },
        ),
      ],
    );
  }

  Widget _buildTabSection(ColorScheme theme) {
    return Column(
      children: [
        // Tab bar
        Container(
          decoration: BoxDecoration(
            border: Border(
              bottom: BorderSide(color: theme.outline.withValues(alpha: 0.2)),
            ),
          ),
          child: TabBar(
            controller: _tabController,
            labelColor: Colors.white,
            unselectedLabelColor: Colors.white60,
            indicatorColor: Colors.blue,
            tabs: const [
              Tab(text: 'Order Book'),
              Tab(text: 'History'),
              Tab(text: 'Info'),
              Tab(text: 'News'),
            ],
          ),
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              OrderbookTab(),
              SymbolHistoryTab(),
              SymbolInfoTab(),
              SymbolNewsTab(),
            ],
          ),
        ),
      ],
    );
  }
}
